// file-explorer/components/agents/auto-execution-config-panel.tsx
// ✅ TASK 6.1: Automatic Execution Configuration UI - Advanced automation settings

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Settings,
  Play,
  Pause,
  AlertTriangle,
  CheckCircle,
  Clock,
  Zap,
  Shield,
  Target,
  Activity,
  Save,
  RotateCcw,
  HelpCircle,
  Info
} from 'lucide-react';
import { AgentUIBridge } from './agent-ui-bridge';
import { useRealTimeMetrics } from './real-time-metrics-provider';

// ✅ Auto-execution Configuration Interface
export interface AutoExecutionConfig {
  enabled: boolean;
  autoApprovalThreshold: number; // 0-100, percentage confidence required for auto-approval
  maxConsecutiveTasks: number; // Maximum tasks to run without user intervention
  taskTimeout: number; // Timeout in seconds for individual tasks
  maxConcurrentTasks: number; // Maximum tasks running simultaneously
  qualityGateThreshold: number; // Minimum quality score required (0-100)
  costLimitPerHour: number; // Maximum cost per hour in USD
  enableSmartThrottling: boolean; // Automatically adjust execution speed based on performance
  enableErrorRecovery: boolean; // Automatically retry failed tasks
  maxRetryAttempts: number; // Maximum retry attempts for failed tasks
  cooldownPeriod: number; // Cooldown period in minutes after errors
}

// ✅ Default configuration
const defaultConfig: AutoExecutionConfig = {
  enabled: false,
  autoApprovalThreshold: 85,
  maxConsecutiveTasks: 5,
  taskTimeout: 300, // 5 minutes
  maxConcurrentTasks: 3,
  qualityGateThreshold: 70,
  costLimitPerHour: 10.0,
  enableSmartThrottling: true,
  enableErrorRecovery: true,
  maxRetryAttempts: 2,
  cooldownPeriod: 5
};

// ✅ Auto-execution Status Interface
interface AutoExecutionStatus {
  isActive: boolean;
  tasksExecuted: number;
  tasksRemaining: number;
  currentCostPerHour: number;
  averageQualityScore: number;
  lastExecutionTime: number;
  errorCount: number;
  isInCooldown: boolean;
  cooldownEndsAt: number;
}

// ✅ Component Props Interface
export interface AutoExecutionConfigPanelProps {
  className?: string;
  onConfigChange?: (config: AutoExecutionConfig) => void;
}

// ✅ AutoExecutionConfigPanel Component
export const AutoExecutionConfigPanel: React.FC<AutoExecutionConfigPanelProps> = ({
  className,
  onConfigChange
}) => {
  const [config, setConfig] = useState<AutoExecutionConfig>(defaultConfig);
  const [status, setStatus] = useState<AutoExecutionStatus>({
    isActive: false,
    tasksExecuted: 0,
    tasksRemaining: 0,
    currentCostPerHour: 0,
    averageQualityScore: 0,
    lastExecutionTime: 0,
    errorCount: 0,
    isInCooldown: false,
    cooldownEndsAt: 0
  });
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // ✅ Ref to access current config without causing re-renders
  const configRef = useRef(config);

  // ✅ Ref to track last metrics update to prevent infinite loops
  const lastMetricsUpdateRef = useRef<number>(0);

  const realTimeMetrics = useRealTimeMetrics();

  // ✅ Update ref when config changes
  useEffect(() => {
    configRef.current = config;
  }, [config]);

  // ✅ Load configuration from storage
  useEffect(() => {
    const loadConfig = () => {
      try {
        const savedConfig = localStorage.getItem('autoExecutionConfig');
        if (savedConfig) {
          const parsed = JSON.parse(savedConfig);
          setConfig({ ...defaultConfig, ...parsed });
        }
      } catch (error) {
        console.error('Failed to load auto-execution config:', error);
      }
    };

    loadConfig();
  }, []);

  // ✅ Update status from real-time metrics - Fixed infinite loop with debouncing
  useEffect(() => {
    if (!realTimeMetrics || realTimeMetrics.lastUpdated === lastMetricsUpdateRef.current) {
      return;
    }

    // Debounce updates to prevent infinite loops
    const timeoutId = setTimeout(() => {
      lastMetricsUpdateRef.current = realTimeMetrics.lastUpdated;

      const estimatedCostPerHour = realTimeMetrics.totalTokensUsed * 0.00002 * 60; // Rough estimate

      setStatus(prevStatus => ({
        ...prevStatus,
        tasksExecuted: realTimeMetrics.successfulTasks,
        currentCostPerHour: estimatedCostPerHour,
        averageQualityScore: realTimeMetrics.systemHealthScore,
        errorCount: realTimeMetrics.totalTasks - realTimeMetrics.successfulTasks,
        tasksRemaining: realTimeMetrics.totalTasks - realTimeMetrics.successfulTasks
      }));
    }, 100); // 100ms debounce

    return () => clearTimeout(timeoutId);
  }, [realTimeMetrics?.lastUpdated]); // Only depend on lastUpdated timestamp

  // ✅ CRITICAL FIX: Sync status.isActive with config.enabled
  useEffect(() => {
    setStatus(prevStatus => ({
      ...prevStatus,
      isActive: config.enabled
    }));
  }, [config.enabled]);

  // ✅ Handle configuration changes - Stabilized with useCallback
  const handleConfigChange = useCallback((key: keyof AutoExecutionConfig, value: any) => {
    setConfig(prevConfig => {
      const newConfig = { ...prevConfig, [key]: value };
      setHasUnsavedChanges(true);

      if (onConfigChange) {
        onConfigChange(newConfig);
      }

      return newConfig;
    });
  }, [onConfigChange]);

  // ✅ Save configuration - Stabilized with useCallback
  const handleSaveConfig = useCallback(async (configToSave?: AutoExecutionConfig) => {
    setIsSaving(true);
    try {
      const currentConfig = configToSave || configRef.current;
      localStorage.setItem('autoExecutionConfig', JSON.stringify(currentConfig));

      // Send to AgentUIBridge if available
      const agentUIBridge = AgentUIBridge.getInstance();
      await agentUIBridge.updateAutoExecutionConfig(currentConfig);

      setHasUnsavedChanges(false);
      console.log('Auto-execution configuration saved');
    } catch (error) {
      console.error('Failed to save auto-execution config:', error);
    } finally {
      setIsSaving(false);
    }
  }, []);

  // ✅ Reset to defaults - Stabilized with useCallback
  const handleResetConfig = useCallback(() => {
    setConfig(defaultConfig);
    setHasUnsavedChanges(true);
  }, []);

  // ✅ Toggle auto-execution - Stabilized with useCallback
  const handleToggleAutoExecution = useCallback(async () => {
    const currentConfig = configRef.current;
    const newEnabled = !currentConfig.enabled;
    const newConfig = { ...currentConfig, enabled: newEnabled };

    setConfig(newConfig);
    setHasUnsavedChanges(true);

    if (onConfigChange) {
      onConfigChange(newConfig);
    }

    if (newEnabled) {
      await handleSaveConfig(newConfig);
    }
  }, [onConfigChange, handleSaveConfig]);

  // ✅ Get safety status color
  const getSafetyStatusColor = () => {
    if (!config.enabled) return 'text-gray-500';
    if (status.errorCount > config.maxRetryAttempts) return 'text-red-500';
    if (status.currentCostPerHour > config.costLimitPerHour * 0.8) return 'text-yellow-500';
    return 'text-green-500';
  };

  // ✅ Calculate execution progress
  const executionProgress = config.maxConsecutiveTasks > 0 ? 
    Math.min((status.tasksExecuted / config.maxConsecutiveTasks) * 100, 100) : 0;

  return (
    <TooltipProvider>
      <div className={`space-y-6 ${className}`}>
        {/* Header with Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Automatic Execution Configuration
              <Tooltip>
                <TooltipTrigger asChild>
                  <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                </TooltipTrigger>
                <TooltipContent className="max-w-sm">
                  <p className="font-medium mb-2">⚡ Automatic Execution</p>
                  <p className="text-sm mb-2">Advanced automation system that executes tasks continuously with intelligent safety controls.</p>
                  <div className="text-xs space-y-1">
                    <p>• <strong>Auto-approval:</strong> Tasks meeting quality thresholds execute automatically</p>
                    <p>• <strong>Safety Limits:</strong> Cost, time, and error rate protection</p>
                    <p>• <strong>Quality Gates:</strong> Minimum standards for task completion</p>
                    <p>• <strong>Smart Throttling:</strong> Adaptive execution speed based on performance</p>
                    <p>• <strong>Error Recovery:</strong> Automatic retry with improved strategies</p>
                  </div>
                </TooltipContent>
              </Tooltip>
              <Badge variant={config.enabled ? 'default' : 'secondary'}>
                {config.enabled ? 'Enabled' : 'Disabled'}
              </Badge>
            </CardTitle>
            <CardDescription>
              Configure automated task execution with safety controls and quality gates
            </CardDescription>
          </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={config.enabled}
                      onCheckedChange={handleToggleAutoExecution}
                    />
                    <Label>Enable Auto-execution</Label>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-sm">Toggle automatic task execution</p>
                  <p className="text-xs text-muted-foreground">When enabled, tasks meeting quality thresholds will execute automatically</p>
                </TooltipContent>
              </Tooltip>
              
              {config.enabled && (
                <div className={`flex items-center gap-2 ${getSafetyStatusColor()}`}>
                  {status.isActive ? (
                    <Play className="h-4 w-4" />
                  ) : status.isInCooldown ? (
                    <Clock className="h-4 w-4" />
                  ) : (
                    <Pause className="h-4 w-4" />
                  )}
                  <span className="text-sm font-medium">
                    {status.isActive ? 'Active' : status.isInCooldown ? 'Cooldown' : 'Standby'}
                  </span>
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              {hasUnsavedChanges && (
                <Badge variant="outline" className="text-yellow-600">
                  Unsaved Changes
                </Badge>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetConfig}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              <Button
                onClick={handleSaveConfig}
                disabled={!hasUnsavedChanges || isSaving}
                size="sm"
              >
                {isSaving ? (
                  <>
                    <Activity className="h-4 w-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configuration Tabs */}
      <Tabs defaultValue="execution" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger value="execution">Execution</TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Configure task execution parameters</p>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger value="quality">Quality</TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Set quality gates and validation thresholds</p>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger value="safety">Safety</TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Configure safety limits and emergency controls</p>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Real-time status and performance monitoring</p>
            </TooltipContent>
          </Tooltip>
        </TabsList>

        <TabsContent value="execution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Execution Settings</CardTitle>
              <CardDescription>Configure how tasks are automatically executed</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Auto-approval Threshold */}
              <div className="space-y-2">
                <Label>Auto-approval Threshold: {config.autoApprovalThreshold}%</Label>
                <Slider
                  value={[config.autoApprovalThreshold]}
                  onValueChange={([value]) => handleConfigChange('autoApprovalThreshold', value)}
                  max={100}
                  min={50}
                  step={5}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Tasks with confidence above this threshold will be auto-approved
                </p>
              </div>

              {/* Max Consecutive Tasks */}
              <div className="space-y-2">
                <Label>Max Consecutive Tasks: {config.maxConsecutiveTasks}</Label>
                <Slider
                  value={[config.maxConsecutiveTasks]}
                  onValueChange={([value]) => handleConfigChange('maxConsecutiveTasks', value)}
                  max={20}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Maximum tasks to execute before requiring user intervention
                </p>
              </div>

              {/* Max Concurrent Tasks */}
              <div className="space-y-2">
                <Label>Max Concurrent Tasks: {config.maxConcurrentTasks}</Label>
                <Slider
                  value={[config.maxConcurrentTasks]}
                  onValueChange={([value]) => handleConfigChange('maxConcurrentTasks', value)}
                  max={10}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Maximum tasks running simultaneously
                </p>
              </div>

              {/* Task Timeout */}
              <div className="space-y-2">
                <Label htmlFor="taskTimeout">Task Timeout (seconds)</Label>
                <Input
                  id="taskTimeout"
                  type="number"
                  value={config.taskTimeout}
                  onChange={(e) => handleConfigChange('taskTimeout', parseInt(e.target.value) || 300)}
                  min={60}
                  max={3600}
                />
                <p className="text-xs text-muted-foreground">
                  Maximum time allowed for individual task execution
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quality" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quality Controls</CardTitle>
              <CardDescription>Set quality gates and validation thresholds</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Quality Gate Threshold */}
              <div className="space-y-2">
                <Label>Quality Gate Threshold: {config.qualityGateThreshold}%</Label>
                <Slider
                  value={[config.qualityGateThreshold]}
                  onValueChange={([value]) => handleConfigChange('qualityGateThreshold', value)}
                  max={100}
                  min={0}
                  step={5}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Minimum quality score required for task completion
                </p>
              </div>

              {/* Smart Throttling */}
              <div className="flex items-center justify-between">
                <div>
                  <Label>Smart Throttling</Label>
                  <p className="text-xs text-muted-foreground">
                    Automatically adjust execution speed based on performance
                  </p>
                </div>
                <Switch
                  checked={config.enableSmartThrottling}
                  onCheckedChange={(checked) => handleConfigChange('enableSmartThrottling', checked)}
                />
              </div>

              {/* Error Recovery */}
              <div className="flex items-center justify-between">
                <div>
                  <Label>Error Recovery</Label>
                  <p className="text-xs text-muted-foreground">
                    Automatically retry failed tasks with improved strategies
                  </p>
                </div>
                <Switch
                  checked={config.enableErrorRecovery}
                  onCheckedChange={(checked) => handleConfigChange('enableErrorRecovery', checked)}
                />
              </div>

              {config.enableErrorRecovery && (
                <div className="space-y-2">
                  <Label>Max Retry Attempts: {config.maxRetryAttempts}</Label>
                  <Slider
                    value={[config.maxRetryAttempts]}
                    onValueChange={([value]) => handleConfigChange('maxRetryAttempts', value)}
                    max={5}
                    min={1}
                    step={1}
                    className="w-full"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="safety" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Safety Controls</CardTitle>
              <CardDescription>Configure safety limits and emergency controls</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Cost Limit */}
              <div className="space-y-2">
                <Label htmlFor="costLimit">Cost Limit per Hour (USD)</Label>
                <Input
                  id="costLimit"
                  type="number"
                  value={config.costLimitPerHour}
                  onChange={(e) => handleConfigChange('costLimitPerHour', parseFloat(e.target.value) || 10.0)}
                  min={0.1}
                  max={100}
                  step={0.1}
                />
                <p className="text-xs text-muted-foreground">
                  Execution will pause if estimated hourly cost exceeds this limit
                </p>
              </div>

              {/* Cooldown Period */}
              <div className="space-y-2">
                <Label>Cooldown Period: {config.cooldownPeriod} minutes</Label>
                <Slider
                  value={[config.cooldownPeriod]}
                  onValueChange={([value]) => handleConfigChange('cooldownPeriod', value)}
                  max={60}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <p className="text-xs text-muted-foreground">
                  Pause duration after errors or safety threshold breaches
                </p>
              </div>

              {/* Safety Status */}
              <div className="p-4 border rounded-lg bg-muted/50">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium">Safety Status</span>
                </div>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-muted-foreground">Current Cost/Hour:</span>
                    <span className="ml-2 font-medium">${status.currentCostPerHour.toFixed(2)}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Error Count:</span>
                    <span className="ml-2 font-medium">{status.errorCount}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Quality Score:</span>
                    <span className="ml-2 font-medium">{status.averageQualityScore.toFixed(1)}%</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Tasks Executed:</span>
                    <span className="ml-2 font-medium">{status.tasksExecuted}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="monitoring" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Real-time Monitoring</CardTitle>
              <CardDescription>Monitor auto-execution progress and performance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Execution Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Execution Progress</span>
                  <span>{status.tasksExecuted}/{config.maxConsecutiveTasks} tasks</span>
                </div>
                <Progress value={executionProgress} className="h-3" />
              </div>

              {/* Current Status */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 border rounded-lg text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Target className="h-4 w-4 text-blue-500" />
                    <span className="text-sm font-medium">Tasks Completed</span>
                  </div>
                  <div className="text-lg font-bold">{status.tasksExecuted}</div>
                </div>
                
                <div className="p-3 border rounded-lg text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium">Quality Score</span>
                  </div>
                  <div className="text-lg font-bold">{status.averageQualityScore.toFixed(1)}%</div>
                </div>
                
                <div className="p-3 border rounded-lg text-center">
                  <div className="flex items-center justify-center gap-2 mb-1">
                    <Zap className="h-4 w-4 text-yellow-500" />
                    <span className="text-sm font-medium">Cost/Hour</span>
                  </div>
                  <div className="text-lg font-bold">${status.currentCostPerHour.toFixed(2)}</div>
                </div>
              </div>

              {/* Status Indicators */}
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Auto-execution Active</span>
                  <Badge variant={status.isActive ? 'default' : 'secondary'}>
                    {status.isActive ? 'Yes' : 'No'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Within Cost Limits</span>
                  <Badge variant={status.currentCostPerHour <= config.costLimitPerHour ? 'default' : 'destructive'}>
                    {status.currentCostPerHour <= config.costLimitPerHour ? 'Yes' : 'No'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between p-2 border rounded">
                  <span className="text-sm">Quality Gate Passed</span>
                  <Badge variant={status.averageQualityScore >= config.qualityGateThreshold ? 'default' : 'destructive'}>
                    {status.averageQualityScore >= config.qualityGateThreshold ? 'Yes' : 'No'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
    </TooltipProvider>
  );
};

export default AutoExecutionConfigPanel;
