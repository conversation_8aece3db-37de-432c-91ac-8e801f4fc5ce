// components/agents/agent-execution-trace.tsx
// ✅ Task 67: Agent Execution Trace & Debug View

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  Filter,
  Search,
  Eye,
  EyeOff,
  Clock,
  FileText,
  Terminal,
  Database,
  Zap,
  AlertTriangle,
  CheckCircle,
  XCircle,
  HelpCircle,
  Info
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// ✅ Task 67: Execution Log Event Types
export interface ExecutionLogEvent {
  id: string;
  timestamp: number;
  agentId: string;
  action: 'thought' | 'file_operation' | 'api_call' | 'vector_lookup' | 'error' | 'retry' | 'completion' | 'terminal_command' | 'shell_command';
  targetFile?: string;
  details: string;
  metadata?: Record<string, any>;
  duration?: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
}

// ✅ Task 67: Execution Log Store
class ExecutionLogStore {
  private static instance: ExecutionLogStore;
  private events: ExecutionLogEvent[] = [];
  private listeners: ((events: ExecutionLogEvent[]) => void)[] = [];
  private maxEvents = 1000; // Keep last 1000 events

  static getInstance(): ExecutionLogStore {
    if (!ExecutionLogStore.instance) {
      ExecutionLogStore.instance = new ExecutionLogStore();
    }
    return ExecutionLogStore.instance;
  }

  logEvent(event: Omit<ExecutionLogEvent, 'id' | 'timestamp'>): void {
    const logEvent: ExecutionLogEvent = {
      ...event,
      id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now()
    };

    this.events.unshift(logEvent); // Add to beginning

    // Keep only maxEvents
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents);
    }

    // Notify listeners
    this.listeners.forEach(listener => listener([...this.events]));

    console.log(`🔍 Agent Execution Log: ${event.agentId} - ${event.action}`, logEvent);
  }

  getEvents(): ExecutionLogEvent[] {
    return [...this.events];
  }

  subscribe(listener: (events: ExecutionLogEvent[]) => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  clear(): void {
    this.events = [];
    this.listeners.forEach(listener => listener([]));
  }

  // ✅ Task 67: Replay functionality
  getEventsForAgent(agentId: string): ExecutionLogEvent[] {
    return this.events.filter(event => event.agentId === agentId);
  }

  getEventsForTimeRange(startTime: number, endTime: number): ExecutionLogEvent[] {
    return this.events.filter(event =>
      event.timestamp >= startTime && event.timestamp <= endTime
    );
  }

  // ✅ Task 86: Record LLM completion with detailed logging
  record(data: {
    agentId: string;
    taskId?: string;
    cardId?: string;
    output: string;
    modelUsed: string;
    tokensUsed: number;
    provider?: string;
    executionTime?: number;
    success?: boolean;
  }): void {
    this.logEvent({
      agentId: data.agentId,
      action: 'completion',
      details: `LLM completion: ${data.modelUsed} (${data.tokensUsed} tokens)`,
      status: data.success !== false ? 'completed' : 'failed',
      duration: data.executionTime,
      metadata: {
        taskId: data.taskId,
        cardId: data.cardId,
        output: data.output.substring(0, 500), // Truncate for storage
        modelUsed: data.modelUsed,
        tokensUsed: data.tokensUsed,
        provider: data.provider,
        outputLength: data.output.length,
        llmCompletion: true
      }
    });
  }
}

// ✅ Global execution logger instance
export const executionLogger = ExecutionLogStore.getInstance();

// ✅ Task 67: Debug Panel Props
interface AgentExecutionTraceProps {
  className?: string;
}

// ✅ Task 67: Main Debug Panel Component
export const AgentExecutionTrace: React.FC<AgentExecutionTraceProps> = ({ className }) => {
  const [events, setEvents] = useState<ExecutionLogEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<ExecutionLogEvent[]>([]);
  const [isVisible, setIsVisible] = useState(true);
  const [filterAgent, setFilterAgent] = useState<string>('all');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [isReplaying, setIsReplaying] = useState(false);
  const [replayIndex, setReplayIndex] = useState(0);

  // ✅ Subscribe to execution log events
  useEffect(() => {
    const unsubscribe = executionLogger.subscribe(setEvents);
    setEvents(executionLogger.getEvents());
    return unsubscribe;
  }, []);

  // ✅ Filter events based on criteria
  useEffect(() => {
    let filtered = events;

    // Filter by agent
    if (filterAgent !== 'all') {
      filtered = filtered.filter(event => event.agentId === filterAgent);
    }

    // Filter by action
    if (filterAction !== 'all') {
      filtered = filtered.filter(event => event.action === filterAction);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(event =>
        event.details.toLowerCase().includes(query) ||
        event.agentId.toLowerCase().includes(query) ||
        event.targetFile?.toLowerCase().includes(query)
      );
    }

    setFilteredEvents(filtered);
  }, [events, filterAgent, filterAction, searchQuery]);

  // ✅ Get unique agents for filter dropdown
  const uniqueAgents = Array.from(new Set(events.map(event => event.agentId)));
  const uniqueActions = Array.from(new Set(events.map(event => event.action)));

  // ✅ Format timestamp
  const formatTimestamp = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  // ✅ Get action icon
  const getActionIcon = (action: string) => {
    switch (action) {
      case 'thought': return <Zap className="h-4 w-4 text-blue-500" />;
      case 'file_operation': return <FileText className="h-4 w-4 text-green-500" />;
      case 'api_call': return <Database className="h-4 w-4 text-purple-500" />;
      case 'vector_lookup': return <Search className="h-4 w-4 text-orange-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      case 'retry': return <RotateCcw className="h-4 w-4 text-yellow-500" />;
      case 'completion': return <CheckCircle className="h-4 w-4 text-green-600" />;
      default: return <Terminal className="h-4 w-4 text-gray-500" />;
    }
  };

  // ✅ Get status badge variant
  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'completed': return 'default';
      case 'failed': return 'destructive';
      case 'running': return 'secondary';
      default: return 'outline';
    }
  };

  // ✅ Clear all events
  const handleClear = () => {
    executionLogger.clear();
  };

  // ✅ Replay functionality (non-destructive)
  const handleReplay = () => {
    if (filteredEvents.length === 0) return;

    setIsReplaying(true);
    setReplayIndex(0);

    // Simulate replay by highlighting events in sequence
    const replayInterval = setInterval(() => {
      setReplayIndex(prev => {
        if (prev >= filteredEvents.length - 1) {
          clearInterval(replayInterval);
          setIsReplaying(false);
          return prev;
        }
        return prev + 1;
      });
    }, 500); // 500ms between steps
  };

  const handleStopReplay = () => {
    setIsReplaying(false);
    setReplayIndex(0);
  };

  if (!isVisible) {
    return (
      <Button
        variant="outline"
        size="sm"
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 z-50"
      >
        <Eye className="h-4 w-4 mr-2" />
        Show Debug Trace
      </Button>
    );
  }

  return (
    <TooltipProvider>
      <Card className={`${className} bg-background/95 backdrop-blur-sm border-border/50`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center gap-2">
                <Terminal className="h-5 w-5" />
                Agent Execution Trace
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-sm">
                    <p className="font-medium mb-2">🔍 Agent Execution Trace</p>
                    <p className="text-sm mb-2">Real-time debugging tool that logs every action performed by agents.</p>
                    <div className="text-xs space-y-1">
                      <p>• <strong>Live Monitoring:</strong> See agent actions as they happen</p>
                      <p>• <strong>Filtering:</strong> Filter by agent, action type, or search terms</p>
                      <p>• <strong>Replay:</strong> Step through execution sequence</p>
                      <p>• <strong>Metadata:</strong> Detailed information about each action</p>
                      <p>• <strong>Performance:</strong> Execution times and status tracking</p>
                    </div>
                  </TooltipContent>
                </Tooltip>
                <Badge variant="outline" className="text-xs">
                  {filteredEvents.length} events
                </Badge>
              </CardTitle>
              <CardDescription>
                Real-time agent action logging and debug visualization
              </CardDescription>
            </div>
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={isReplaying ? handleStopReplay : handleReplay}
                  disabled={filteredEvents.length === 0}
                >
                  {isReplaying ? (
                    <>
                      <Square className="h-4 w-4 mr-2" />
                      Stop Replay
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Replay
                    </>
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">Replay execution sequence step by step</p>
                <p className="text-xs text-muted-foreground">Highlights events in chronological order for debugging</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="outline" size="sm" onClick={handleClear}>
                  Clear
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">Clear all execution events</p>
                <p className="text-xs text-muted-foreground">Removes all logged events from the trace</p>
              </TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsVisible(false)}
                >
                  <EyeOff className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-sm">Hide debug trace panel</p>
                <p className="text-xs text-muted-foreground">Minimizes the panel to save screen space</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* ✅ Filters */}
        <div className="flex gap-2 flex-wrap">
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="max-w-xs"
              />
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Search through event details, agent names, and file paths</p>
              <p className="text-xs text-muted-foreground">Real-time filtering as you type</p>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Select value={filterAgent} onValueChange={setFilterAgent}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by agent" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Agents</SelectItem>
                  {uniqueAgents.map(agent => (
                    <SelectItem key={agent} value={agent}>{agent}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Filter events by specific agent</p>
              <p className="text-xs text-muted-foreground">Focus on individual agent execution traces</p>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger asChild>
              <Select value={filterAction} onValueChange={setFilterAction}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Actions</SelectItem>
                  {uniqueActions.map(action => (
                    <SelectItem key={action} value={action}>{action}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </TooltipTrigger>
            <TooltipContent>
              <p className="text-sm">Filter by action type (thought, file_operation, api_call, etc.)</p>
              <p className="text-xs text-muted-foreground">Isolate specific types of agent actions</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* ✅ Events List */}
        <ScrollArea className="h-[400px] w-full border rounded-lg">
          <div className="p-4 space-y-2">
            {filteredEvents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <Terminal className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No execution events to display</p>
                <p className="text-sm">Agent actions will appear here in real-time</p>
              </div>
            ) : (
              filteredEvents.map((event, index) => (
                <div
                  key={event.id}
                  className={`p-3 border rounded-lg transition-all ${
                    isReplaying && index === replayIndex
                      ? 'bg-blue-50 dark:bg-blue-950/50 border-blue-200 dark:border-blue-800'
                      : 'bg-muted/30'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getActionIcon(event.action)}
                      <span className="font-medium text-sm">{event.agentId}</span>
                      <Badge variant={getStatusVariant(event.status)} className="text-xs">
                        {event.status}
                      </Badge>
                      {event.targetFile && (
                        <Badge variant="outline" className="text-xs">
                          {event.targetFile}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {formatTimestamp(event.timestamp)}
                      {event.duration && (
                        <span>({event.duration}ms)</span>
                      )}
                    </div>
                  </div>
                  <p className="text-sm text-foreground">{event.details}</p>
                  {event.metadata && Object.keys(event.metadata).length > 0 && (
                    <details className="mt-2">
                      <summary className="text-xs text-muted-foreground cursor-pointer">
                        Metadata
                      </summary>
                      <pre className="text-xs bg-muted/50 p-2 rounded mt-1 overflow-x-auto">
                        {JSON.stringify(event.metadata, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
    </TooltipProvider>
  );
};

export default AgentExecutionTrace;
