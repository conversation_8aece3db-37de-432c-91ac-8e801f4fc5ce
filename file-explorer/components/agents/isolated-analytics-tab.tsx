// file-explorer/components/agents/isolated-analytics-tab.tsx
// ✅ TASK 5.1: Real Analytics Tab - Connected to real agent performance data

"use client"

import React, { useEffect, useState, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { BarChart4, RefreshCw, TrendingUp, TrendingDown, Activity, CheckCircle, AlertTriangle, DollarSign } from 'lucide-react'
import { useRealTimeMetrics } from './real-time-metrics-provider'

interface IsolatedAnalyticsTabProps {
  // Add any props needed for the analytics tab
}

/**
 * ✅ TASK 5.1: Real Analytics Tab Component
 *
 * Replaces static placeholder content with real agent performance analytics
 * Connected to real-time metrics and agent execution data
 */
const IsolatedAnalyticsTab = React.memo<IsolatedAnalyticsTabProps>(() => {
  const realTimeMetrics = useRealTimeMetrics()
  const [isLoading, setIsLoading] = useState(true)

  // ✅ Calculate real analytics using useMemo to prevent infinite loops
  const analyticsData = useMemo(() => {
    if (!realTimeMetrics) return null

    const now = Date.now()
    const oneDayAgo = now - (24 * 60 * 60 * 1000)

    // Calculate task completion trends
    const recentUpdates = realTimeMetrics.executionUpdates.filter(
      update => update.timestamp > oneDayAgo
    )
    const completedTasks = recentUpdates.filter(update => update.type === 'completion')
    const errorTasks = recentUpdates.filter(update => update.type === 'error')

    // Calculate agent performance metrics
    const agentPerformance = realTimeMetrics.agentStatuses.reduce((acc, agent) => {
      acc[agent.agentId] = {
        healthScore: agent.healthScore,
        tasksCompleted: agent.tasksCompleted,
        tokensUsed: agent.tokensUsed,
        errorCount: agent.errorCount,
        successRate: agent.tasksCompleted > 0 ?
          ((agent.tasksCompleted - agent.errorCount) / agent.tasksCompleted) * 100 : 0
      }
      return acc
    }, {} as any)

    // Calculate cost estimates (mock calculation based on tokens)
    const estimatedCostPerToken = 0.00002 // $0.00002 per token (rough estimate)
    const totalCost = realTimeMetrics.totalTokensUsed * estimatedCostPerToken
    const todayCost = recentUpdates.reduce((sum, update) => {
      const agent = realTimeMetrics.agentStatuses.find(a => a.agentId === update.agentId)
      return sum + (agent ? agent.tokensUsed * estimatedCostPerToken * 0.1 : 0) // Rough daily estimate
    }, 0)

    return {
      overview: {
        totalTasks: realTimeMetrics.totalTasks,
        completedTasks: realTimeMetrics.successfulTasks,
        successRate: realTimeMetrics.totalTasks > 0 ?
          (realTimeMetrics.successfulTasks / realTimeMetrics.totalTasks) * 100 : 0,
        activeAgents: realTimeMetrics.activeAgents,
        totalTokensUsed: realTimeMetrics.totalTokensUsed,
        averageResponseTime: realTimeMetrics.averageResponseTime,
        systemHealthScore: realTimeMetrics.systemHealthScore,
        totalCost: totalCost,
        todayCost: todayCost
      },
      trends: {
        tasksToday: completedTasks.length,
        errorsToday: errorTasks.length,
        taskTrend: completedTasks.length > 0 ? 'increase' : 'neutral',
        healthTrend: realTimeMetrics.systemHealthScore > 80 ? 'increase' :
                   realTimeMetrics.systemHealthScore > 60 ? 'neutral' : 'decrease'
      },
      agentPerformance,
      recentActivity: realTimeMetrics.executionUpdates.slice(-10).reverse()
    }
  }, [
    realTimeMetrics?.totalTasks,
    realTimeMetrics?.successfulTasks,
    realTimeMetrics?.activeAgents,
    realTimeMetrics?.totalTokensUsed,
    realTimeMetrics?.systemHealthScore,
    realTimeMetrics?.agentStatuses?.length,
    realTimeMetrics?.executionUpdates?.length
  ]) // Only depend on specific values that matter for analytics

  // ✅ Set loading state based on data availability
  useEffect(() => {
    setIsLoading(!analyticsData)
  }, [analyticsData])

  useEffect(() => {
    console.log('🔄 Real Analytics Tab mounted')

    return () => {
      console.log('🧹 Real Analytics Tab unmounted - cleaning up')
    }
  }, [])

  if (isLoading || !analyticsData) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center space-y-4">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
          <p className="text-muted-foreground">Loading real-time analytics...</p>
        </div>
      </div>
    )
  }

  const { overview, trends, agentPerformance, recentActivity } = analyticsData

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center gap-2">
          <BarChart4 className="h-5 w-5 text-muted-foreground" />
          <h2 className="text-lg font-semibold">Real-time Analytics</h2>
          <Badge variant="outline" className="text-xs">
            Live Data
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => {
            // Refresh analytics data only, not entire application
            setIsLoading(true);
            // Force re-fetch of real-time metrics
            setTimeout(() => setIsLoading(false), 1000);
          }}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4 space-y-6">
        {/* Overview Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Tasks</p>
                  <p className="text-2xl font-bold">{overview.totalTasks}</p>
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    +{trends.tasksToday} today
                  </div>
                </div>
                <CheckCircle className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                  <p className="text-2xl font-bold">{overview.successRate.toFixed(1)}%</p>
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    <TrendingUp className="h-3 w-3" />
                    {overview.completedTasks}/{overview.totalTasks} completed
                  </div>
                </div>
                <Activity className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">System Health</p>
                  <p className="text-2xl font-bold">{overview.systemHealthScore.toFixed(1)}%</p>
                  <div className="flex items-center gap-1 text-xs">
                    {trends.healthTrend === 'increase' ? (
                      <TrendingUp className="h-3 w-3 text-green-600" />
                    ) : trends.healthTrend === 'decrease' ? (
                      <TrendingDown className="h-3 w-3 text-red-600" />
                    ) : (
                      <div className="h-3 w-3" />
                    )}
                    <span className={
                      trends.healthTrend === 'increase' ? 'text-green-600' :
                      trends.healthTrend === 'decrease' ? 'text-red-600' : 'text-muted-foreground'
                    }>
                      {overview.activeAgents} active agents
                    </span>
                  </div>
                </div>
                <BarChart4 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Estimated Cost</p>
                  <p className="text-2xl font-bold">${overview.totalCost.toFixed(2)}</p>
                  <div className="flex items-center gap-1 text-xs text-blue-600">
                    <DollarSign className="h-3 w-3" />
                    ${overview.todayCost.toFixed(2)} today
                  </div>
                </div>
                <DollarSign className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Agent Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Agent Performance</CardTitle>
            <CardDescription>Real-time performance metrics for each agent</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(agentPerformance).map(([agentId, performance]: [string, any]) => (
                <div key={agentId} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium">{agentId}</span>
                      <Badge variant={performance.healthScore >= 80 ? 'default' :
                                   performance.healthScore >= 60 ? 'secondary' : 'destructive'}>
                        {performance.healthScore.toFixed(0)}% Health
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Tasks:</span>
                        <span className="ml-1 font-medium">{performance.tasksCompleted}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Success Rate:</span>
                        <span className="ml-1 font-medium">{performance.successRate.toFixed(1)}%</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Tokens:</span>
                        <span className="ml-1 font-medium">{performance.tokensUsed.toLocaleString()}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Errors:</span>
                        <span className="ml-1 font-medium">{performance.errorCount}</span>
                      </div>
                    </div>
                    <div className="mt-2">
                      <Progress value={performance.healthScore} className="h-2" />
                    </div>
                  </div>
                </div>
              ))}

              {Object.keys(agentPerformance).length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No agent performance data available</p>
                  <p className="text-sm">Agents will appear here once they start executing tasks</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recent Activity</CardTitle>
            <CardDescription>Latest execution updates from agents</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {recentActivity.map((update: any, index: number) => (
                <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0 mt-1">
                    {update.type === 'completion' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : update.type === 'error' ? (
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                    ) : (
                      <Activity className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium">{update.agentId}</span>
                      <Badge variant="outline" className="text-xs">
                        {update.type}
                      </Badge>
                      <span className="text-xs text-muted-foreground ml-auto">
                        {new Date(update.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {update.data.message || `${update.type} update`}
                    </div>
                    {update.data.filePath && (
                      <div className="text-xs text-blue-600 mt-1 font-mono">
                        📁 {update.data.filePath}
                      </div>
                    )}
                  </div>
                </div>
              ))}

              {recentActivity.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No recent activity</p>
                  <p className="text-sm">Agent execution updates will appear here</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Token Usage Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Token Usage Analysis</CardTitle>
            <CardDescription>Token consumption breakdown by agent</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {realTimeMetrics.agentStatuses.map((agent) => (
                <div key={agent.agentId} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 rounded-full bg-blue-500" />
                    <span className="font-medium">{agent.name}</span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm text-muted-foreground">
                      {agent.tokensUsed.toLocaleString()} tokens
                    </span>
                    <span className="text-sm font-medium">
                      ${(agent.tokensUsed * 0.00002).toFixed(3)}
                    </span>
                  </div>
                </div>
              ))}

              <div className="border-t pt-4">
                <div className="flex items-center justify-between font-medium">
                  <span>Total</span>
                  <div className="flex items-center gap-4">
                    <span>{overview.totalTokensUsed.toLocaleString()} tokens</span>
                    <span>${overview.totalCost.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Performance Insights</CardTitle>
            <CardDescription>Automated insights based on real agent data</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* System Health Insight */}
              <div className="flex items-start gap-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 mt-1">
                  {overview.systemHealthScore >= 80 ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : overview.systemHealthScore >= 60 ? (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">System Health Status</h4>
                  <p className="text-sm text-muted-foreground">
                    {overview.systemHealthScore >= 80
                      ? "System is performing optimally with high agent health scores."
                      : overview.systemHealthScore >= 60
                      ? "System performance is moderate. Consider monitoring agent workloads."
                      : "System health is below optimal. Review agent performance and error rates."}
                  </p>
                </div>
              </div>

              {/* Task Success Insight */}
              <div className="flex items-start gap-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 mt-1">
                  {overview.successRate >= 90 ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : overview.successRate >= 70 ? (
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  ) : (
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  )}
                </div>
                <div>
                  <h4 className="font-medium">Task Success Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    {overview.successRate >= 90
                      ? "Excellent task success rate. Agents are performing reliably."
                      : overview.successRate >= 70
                      ? "Good task success rate. Monitor for potential improvements."
                      : "Task success rate needs attention. Review failed tasks and agent configurations."}
                  </p>
                </div>
              </div>

              {/* Cost Efficiency Insight */}
              <div className="flex items-start gap-3 p-3 border rounded-lg">
                <div className="flex-shrink-0 mt-1">
                  <DollarSign className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium">Cost Efficiency</h4>
                  <p className="text-sm text-muted-foreground">
                    Current token usage: {overview.totalTokensUsed.toLocaleString()} tokens
                    (${overview.totalCost.toFixed(2)} estimated cost).
                    Average cost per task: ${(overview.totalCost / Math.max(overview.totalTasks, 1)).toFixed(3)}.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
})

IsolatedAnalyticsTab.displayName = 'IsolatedAnalyticsTab'

export default IsolatedAnalyticsTab
